#!/usr/bin/env python3

"""
Test script to verify the new Bybit downloader implementation
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from downloader import OHLCVManager
import logging

logging.basicConfig(level=logging.INFO)

async def test_bybit_downloader():
    """Test the new Bybit downloader with official API"""
    
    # Test with a small date range and rate limit override
    om = OHLCVManager(
        exchange="bybit",
        start_date="2024-01-01",
        end_date="2024-01-02",
        rate_limit_override=30,  # Conservative rate limit for testing
        verbose=True
    )
    
    try:
        await om.load_markets()
        print(f"Loaded {len(om.markets)} markets")
        
        # Test with a popular coin
        test_coin = "BTC"
        
        print(f"Testing download for {test_coin}")
        
        # Check if coin exists
        if om.has_coin(test_coin):
            print(f"✓ {test_coin} is available on Bybit")
            
            # Test finding first timestamp
            first_ts = await om.get_first_timestamp(test_coin)
            print(f"✓ First timestamp for {test_coin}: {first_ts}")
            
            # Test downloading OHLCV data
            df = await om.get_ohlcvs(test_coin)
            print(f"✓ Downloaded {len(df)} candles for {test_coin}")
            
            if len(df) > 0:
                print(f"✓ Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
                print(f"✓ Sample data:")
                print(df.head())
            else:
                print("⚠ No data returned")
                
        else:
            print(f"✗ {test_coin} not available on Bybit")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if om.cc:
            await om.cc.close()

if __name__ == "__main__":
    asyncio.run(test_bybit_downloader())
