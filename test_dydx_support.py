#!/usr/bin/env python3

"""
Test script to verify DYDX support in the downloader
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from downloader import OHLCVManager
import logging

logging.basicConfig(level=logging.INFO)

async def test_dydx_support():
    """Test DYDX exchange support"""
    
    print("Testing DYDX exchange support...")
    
    # Test with a small date range
    om = OHLCVManager(
        exchange="dydx",
        start_date="2024-01-01",
        end_date="2024-01-02",
        rate_limit_override=30,  # Conservative rate limit for testing
        verbose=True
    )
    
    try:
        # Test market loading
        print("Loading markets...")
        await om.load_markets()
        print(f"✓ Loaded {len(om.markets)} markets")
        
        # Show some available markets
        if om.markets:
            print("Available markets:")
            for i, symbol in enumerate(list(om.markets.keys())[:5]):  # Show first 5
                print(f"  - {symbol}")
            if len(om.markets) > 5:
                print(f"  ... and {len(om.markets) - 5} more")
        
        # Test with a popular coin
        test_coin = "BTC"
        
        print(f"\nTesting with {test_coin}...")
        
        # Check if coin exists
        if om.has_coin(test_coin):
            print(f"✓ {test_coin} is available on DYDX")
            
            # Test symbol conversion
            symbol = om.get_symbol(test_coin)
            print(f"✓ Symbol for {test_coin}: {symbol}")
            
            # Test market specific settings
            mss = om.get_market_specific_settings(test_coin)
            print(f"✓ Market settings: maker_fee={mss['maker_fee']}, taker_fee={mss['taker_fee']}")
            
            # Test finding first timestamp
            print("Finding first timestamp...")
            first_ts = await om.get_first_timestamp(test_coin)
            print(f"✓ First timestamp for {test_coin}: {first_ts}")
            
            # Test API call
            print("Testing API call...")
            candles = await om.fetch_dydx_candles(
                om.get_dydx_symbol(test_coin),
                resolution="1DAY",
                limit=1
            )
            if candles:
                print(f"✓ API call successful, got {len(candles)} candles")
                print(f"  Sample candle: {candles[0]}")
            else:
                print("⚠ No candles returned from API")
                
        else:
            print(f"✗ {test_coin} not available on DYDX")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_dydx_support())
