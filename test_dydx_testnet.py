#!/usr/bin/env python3

"""
Test script to verify DYDX testnet support in the downloader
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from downloader import OHLCVManager
import logging

logging.basicConfig(level=logging.INFO)

async def test_dydx_testnet_support():
    """Test DYDX testnet exchange support"""
    
    print("Testing DYDX testnet exchange support...")
    
    # Test with a small date range
    om = OHLCVManager(
        exchange="dydx_testnet",
        start_date="2024-01-01",
        end_date="2024-01-02",
        rate_limit_override=30,  # Conservative rate limit for testing
        verbose=True
    )
    
    try:
        # Test market loading
        print("Loading markets from testnet...")
        await om.load_markets()
        print(f"✓ Loaded {len(om.markets)} markets from testnet")
        
        # Show some available markets
        if om.markets:
            print("Available testnet markets:")
            for i, symbol in enumerate(list(om.markets.keys())[:5]):  # Show first 5
                print(f"  - {symbol}")
            if len(om.markets) > 5:
                print(f"  ... and {len(om.markets) - 5} more")
        
        # Test with a popular coin
        test_coin = "BTC"
        
        print(f"\nTesting with {test_coin} on testnet...")
        
        # Check if coin exists
        if om.has_coin(test_coin):
            print(f"✓ {test_coin} is available on DYDX testnet")
            
            # Test symbol conversion
            symbol = om.get_symbol(test_coin)
            print(f"✓ Symbol for {test_coin}: {symbol}")
            
            # Test market specific settings
            mss = om.get_market_specific_settings(test_coin)
            print(f"✓ Market settings: maker_fee={mss['maker_fee']}, taker_fee={mss['taker_fee']}")
            
            # Test API call
            print("Testing testnet API call...")
            candles = await om.fetch_dydx_candles(
                om.get_dydx_symbol(test_coin),
                resolution="1DAY",
                limit=1
            )
            if candles:
                print(f"✓ Testnet API call successful, got {len(candles)} candles")
                print(f"  Sample candle: {candles[0]}")
            else:
                print("⚠ No candles returned from testnet API")
                
        else:
            print(f"✗ {test_coin} not available on DYDX testnet")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

async def compare_mainnet_testnet():
    """Compare mainnet and testnet endpoints"""
    
    print("\n" + "="*50)
    print("Comparing DYDX mainnet vs testnet...")
    
    # Test mainnet
    print("\nTesting mainnet...")
    om_mainnet = OHLCVManager(
        exchange="dydx",
        start_date="2024-01-01",
        end_date="2024-01-02",
        rate_limit_override=30,
        verbose=False
    )
    
    try:
        await om_mainnet.load_markets()
        mainnet_markets = len(om_mainnet.markets)
        print(f"✓ Mainnet: {mainnet_markets} markets")
    except Exception as e:
        print(f"✗ Mainnet error: {e}")
        mainnet_markets = 0
    
    # Test testnet
    print("Testing testnet...")
    om_testnet = OHLCVManager(
        exchange="dydx_testnet",
        start_date="2024-01-01",
        end_date="2024-01-02",
        rate_limit_override=30,
        verbose=False
    )
    
    try:
        await om_testnet.load_markets()
        testnet_markets = len(om_testnet.markets)
        print(f"✓ Testnet: {testnet_markets} markets")
    except Exception as e:
        print(f"✗ Testnet error: {e}")
        testnet_markets = 0
    
    print(f"\nComparison:")
    print(f"  Mainnet markets: {mainnet_markets}")
    print(f"  Testnet markets: {testnet_markets}")
    
    if mainnet_markets > 0 and testnet_markets > 0:
        print("✓ Both mainnet and testnet are working!")
    elif mainnet_markets > 0:
        print("⚠ Only mainnet is working")
    elif testnet_markets > 0:
        print("⚠ Only testnet is working")
    else:
        print("✗ Neither mainnet nor testnet is working")

if __name__ == "__main__":
    asyncio.run(test_dydx_testnet_support())
    asyncio.run(compare_mainnet_testnet())
