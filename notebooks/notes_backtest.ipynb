#%%
%load_ext autoreload
%autoreload 2
%run notebook_setup.py
#%%
import sys
import os
from backtest import *
#%%
config = load_config("configs/template.json")
config["backtest"]["use_btc_collateral"] = True
config["backtest"]["combine_ohlcvs"] = True
{k: config[k] for k in ["backtest", "bot", "live"]}
#%%
config["backtest"]["start_date"] = "2023-01-01"
# config['backtest']['end_date'] = '2025-03-20'
config["backtest"]["exchanges"] = ["binance", "bybit"]
exchange = "combined" if config["backtest"]["combine_ohlcvs"] else config["backtest"]["exchanges"][0]
#%%
coins, hlcvs, mss, results_path, cache_dir, btc_usd_prices = await prepare_hlcvs_mss(config, exchange)
config["backtest"]["coins"] = {exchange: coins}
#%%
# config['bot']['long']['n_positions'] = 3
# config['bot']['long']['filter_rolling_window'] = 10
# config['bot']['long']['filter_relative_volume_clip_pct'] = 0.5
# config['bot']['short']['n_positions'] = 0.0
#%%
fills, equities_usd, equities_btc, analysis = run_backtest(
    hlcvs, mss, config, exchange, btc_usd_prices
)
#%%
fdf, analysis_py, bal_eq = process_forager_fills(
    fills,
    config["backtest"]["coins"][exchange],
    hlcvs,
    equities_usd,
    equities_btc,
)
for k in analysis_py:
    if k not in analysis:
        analysis[k] = analysis_py[k]
pprint.pprint(analysis)
if config["backtest"]["use_btc_collateral"]:
    bal_eq[["balance_btc", "equity_btc"]].plot()
else:
    bal_eq[["balance", "equity"]].plot()
#%%
coins_sorted_by_volume = fdf.groupby("coin").fee_paid.sum().sort_values().index.to_list()
for i, coin in enumerate(coins_sorted_by_volume[:5]):
    print(f"Plotting fills for {coin}")
    hlcvs_df = pd.DataFrame(
        hlcvs[:, coins.index(coin), :], columns=["high", "low", "close", "volume"]
    )
    fdfc = fdf[fdf.coin == coin]
    plt.clf()
    plot_fills_forager(fdfc, hlcvs_df)
    plt.title(f"Fills {coin}")
    plt.xlabel = "time"
    plt.ylabel = "price"
    plt.show()
#%%
# performers worst to best
for x in fdf.groupby("coin").pnl.sum().sort_values().to_dict().items():
    print(x)
#%%
