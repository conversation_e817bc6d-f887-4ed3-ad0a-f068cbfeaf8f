{"user": "VirtualShowdown", "exchange_name": "bybit", "bot_name": "passivbot_hyperliquid_top10_mcap_from_template", "backtest": {"base_dir": "backtests/hyperliquid_top10_mcap", "combine_ohlcvs": true, "compress_cache": true, "end_date": "now", "exchanges": ["bybit"], "gap_tolerance_ohlcvs_minutes": 120, "start_date": "2023-01-01", "starting_balance": 10000, "use_btc_collateral": false}, "live": {"user": "your_api_key_name_in_api-keys.json", "approved_coins": ["ETH"], "ignored_coins": [], "empty_means_all_approved": false, "auto_gs": true, "coin_flags": {}, "leverage": 10, "market_orders_allowed": true, "mimic_backtest_1m_delay": true, "filter_by_min_effective_cost": true, "minimum_coin_age_days": 7, "execution_delay_seconds": 0, "max_n_cancellations_per_batch": 10, "max_n_creations_per_batch": 10, "max_n_restarts_per_day": 5, "ohlcvs_1m_rolling_window_days": 30, "ohlcvs_1m_update_after_minutes": 5, "pnls_max_lookback_days": 90, "price_distance_threshold": 0.0005, "time_in_force": "gtc", "forced_mode_long": null, "forced_mode_short": null}, "bot": {"long": {"n_positions": 5, "total_wallet_exposure_limit": 0.9, "enforce_exposure_limit": true, "ema_span_0": 450, "ema_span_1": 1300, "entry_initial_ema_dist": 0.002, "entry_initial_qty_pct": 0.01, "entry_grid_spacing_pct": 0.003, "entry_grid_spacing_weight": 1.0, "entry_grid_double_down_factor": 0.8, "entry_trailing_grid_ratio": 0.4, "entry_trailing_threshold_pct": 0.004, "entry_trailing_retracement_pct": 0.002, "entry_trailing_double_down_factor": 1.364, "close_grid_markup_start": 0.006, "close_grid_markup_end": 0.002, "close_grid_qty_pct": 0.2, "close_trailing_grid_ratio": 0.4, "close_trailing_qty_pct": 0.2, "close_trailing_threshold_pct": 0.004, "close_trailing_retracement_pct": 0.002, "unstuck_threshold": 0.8, "unstuck_ema_dist": 0.002, "unstuck_close_pct": 0.05, "unstuck_loss_allowance_pct": 0.01, "filter_volume_drop_pct": 0.1, "filter_noisiness_rolling_window": 1440, "filter_volume_rolling_window": 1440}, "short": {"n_positions": 5, "total_wallet_exposure_limit": 0.9, "enforce_exposure_limit": true, "ema_span_0": 450, "ema_span_1": 1300, "entry_initial_ema_dist": 0.002, "entry_initial_qty_pct": 0.01, "entry_grid_spacing_pct": 0.003, "entry_grid_spacing_weight": 1.0, "entry_grid_double_down_factor": 0.8, "entry_trailing_grid_ratio": 0.4, "entry_trailing_threshold_pct": 0.004, "entry_trailing_retracement_pct": 0.002, "entry_trailing_double_down_factor": 1.364, "close_grid_markup_start": 0.006, "close_grid_markup_end": 0.002, "close_grid_qty_pct": 0.2, "close_trailing_grid_ratio": 0.4, "close_trailing_qty_pct": 0.2, "close_trailing_threshold_pct": 0.004, "close_trailing_retracement_pct": 0.002, "unstuck_threshold": 0.8, "unstuck_ema_dist": 0.002, "unstuck_close_pct": 0.05, "unstuck_loss_allowance_pct": 0.01, "filter_volume_drop_pct": 0.1, "filter_noisiness_rolling_window": 1440, "filter_volume_rolling_window": 1440}}, "optimize": {"iters": 100, "population_size": 50, "n_cpus": -1, "scoring": ["mdg_w", "sharpe_ratio_w"], "crossover_probability": 0.7, "mutation_probability": 0.3, "compress_results_file": true, "enable_overrides": [], "limits": {"penalize_if_greater_than_drawdown_worst": 0.3, "penalize_if_lower_than_adg": 0.0005}, "bounds": {"long_ema_span_0": [200, 800], "long_ema_span_1": [800, 2000], "long_entry_initial_qty_pct": [0.005, 0.05], "long_total_wallet_exposure_limit": [0.5, 1.5], "short_ema_span_0": [200, 800], "short_ema_span_1": [800, 2000], "short_entry_initial_qty_pct": [0.005, 0.05], "short_total_wallet_exposure_limit": [0.5, 1.5]}, "round_to_n_significant_digits": 4, "write_all_results": true}}