from passivbot import Passivbot, logging, get_function_name
from uuid import uuid4
import asyncio
import traceback
import json
import numpy as np
import passivbot_rust as pbr
from pure_funcs import (
    multi_replace,
    floatify,
    ts_to_date_utc,
    calc_hash,
    shorten_custom_id,
    coin2symbol,
    symbol_to_coin,
)
from njit_funcs import (
    calc_diff,
    round_,
    round_up,
    round_dn,
    round_dynamic,
    round_dynamic_up,
    round_dynamic_dn,
)
from procedures import print_async_exception, utc_ms
from sortedcontainers import SortedDict

# Import DYDX v4 client
try:
    from dydx_v4_client import NodeClient, IndexerClient, Wallet, Order, OrderFlags
    from dydx_v4_client.network import TESTNET, make_testnet, make_mainnet
    from dydx_v4_client.wallet import from_mnemonic
    DYDX_AVAILABLE = True
except ImportError as e:
    logging.error(f"DYDX v4 client not available: {e}")
    DYDX_AVAILABLE = False


class DydxBot(Passivbot):
    def __init__(self, config: dict):
        if not DYDX_AVAILABLE:
            raise Exception("DYDX v4 client is not installed. Please install with: uv pip install dydx-v4-client")
        
        super().__init__(config)
        self.quote = "USD"
        self.hedge_mode = False
        self.max_n_concurrent_ohlcvs_1m_updates = 2
        
        # Initialize DYDX client
        self.setup_dydx_client()

    def setup_dydx_client(self):
        """Initialize DYDX v4 client with wallet credentials"""
        try:
            # Determine network based on exchange type
            if self.exchange == "dydx_testnet":
                self.network = make_testnet()
            else:
                self.network = make_mainnet()

            # Create signing key from mnemonic
            signing_key = from_mnemonic(self.user_info["private_key"])

            # Create wallet with the signing key
            self.wallet = Wallet(
                key=signing_key,
                account_number=0,  # Will be updated when needed
                sequence=0         # Will be updated when needed
            )

            # Get wallet address from user info
            self.wallet_address = self.user_info["wallet_address"]

            # Initialize clients using the network configuration
            # For now, we'll focus on the IndexerClient for read-only operations
            if self.exchange == "dydx_testnet":
                indexer_host = "https://indexer.v4testnet.dydx.exchange"
            else:
                indexer_host = "https://indexer.dydx.trade"

            self.indexer_client = IndexerClient(
                host=indexer_host
            )

            # Note: NodeClient setup is more complex and will be implemented when needed for trading
            self.node_client = None  # Will be initialized when needed for order operations

            logging.info(f"DYDX client initialized for {self.exchange}")
            logging.info(f"Wallet address: {self.wallet_address}")

        except Exception as e:
            logging.error(f"Failed to initialize DYDX client: {e}")
            raise

    def create_ccxt_sessions(self):
        """DYDX doesn't use CCXT, so we override this method"""
        # DYDX uses its own client, not CCXT
        # Create a mock cca attribute to satisfy the base class
        self.cca = self
        pass

    async def load_markets(self, reload=False):
        """Load markets from DYDX API"""
        try:
            response = await self.indexer_client.markets.get_perpetual_markets()
            if response and "markets" in response:
                markets = {}
                for market_id, market_data in response["markets"].items():
                    symbol = market_id.replace("-USD", "/USD:USD")

                    # Convert DYDX market data to CCXT-like format
                    markets[symbol] = {
                        "id": market_id,
                        "symbol": symbol,
                        "base": market_id.replace("-USD", ""),
                        "quote": "USD",
                        "settle": "USD",
                        "type": "swap",
                        "spot": False,
                        "margin": False,
                        "swap": True,
                        "future": False,
                        "option": False,
                        "active": market_data.get("status") == "ACTIVE",
                        "contract": True,
                        "linear": True,
                        "inverse": False,
                        "contractSize": 1.0,
                        "expiry": None,
                        "expiryDatetime": None,
                        "strike": None,
                        "optionType": None,
                        "precision": {
                            "amount": float(market_data.get("stepSize", 0.001)),
                            "price": float(market_data.get("tickSize", 0.01)),
                        },
                        "limits": {
                            "amount": {
                                "min": float(market_data.get("minOrderSize", 0.001)),
                                "max": None,
                            },
                            "price": {
                                "min": None,
                                "max": None,
                            },
                            "cost": {
                                "min": 1.0,  # DYDX typically has $1 minimum
                                "max": None,
                            },
                        },
                        "info": market_data,
                    }

                return markets
            return {}
        except Exception as e:
            logging.error(f"Error loading DYDX markets: {e}")
            return {}

    async def fetch_balance(self):
        """Fetch account balance from DYDX"""
        try:
            import time
            account_info = await self.fetch_account_info()
            if account_info:
                equity = float(account_info.get("equity", 0))
                free_collateral = float(account_info.get("freeCollateral", 0))

                return {
                    "USD": {
                        "total": equity,
                        "free": free_collateral,
                        "used": equity - free_collateral
                    },
                    "timestamp": int(time.time() * 1000)  # Add timestamp for UTC offset calculation
                }
            return {
                "USD": {"total": 0.0, "free": 0.0, "used": 0.0},
                "timestamp": int(time.time() * 1000)
            }
        except Exception as e:
            logging.error(f"Error fetching balance: {e}")
            return {
                "USD": {"total": 0.0, "free": 0.0, "used": 0.0},
                "timestamp": int(time.time() * 1000)
            }

    def set_market_specific_settings(self):
        """Set market-specific settings for DYDX"""
        super().set_market_specific_settings()
        
        for symbol in self.markets_dict:
            elm = self.markets_dict[symbol]
            self.symbol_ids[symbol] = elm["id"]
            
            # Set minimum costs (DYDX typically has $1 minimum)
            self.min_costs[symbol] = (
                1.0 if elm["limits"]["cost"]["min"] is None else elm["limits"]["cost"]["min"]
            )
            self.min_costs[symbol] = pbr.round_(self.min_costs[symbol] * 1.01, 0.01)
            
            # Set quantity and price steps
            self.qty_steps[symbol] = elm["precision"]["amount"]
            self.min_qtys[symbol] = (
                self.qty_steps[symbol]
                if elm["limits"]["amount"]["min"] is None
                else elm["limits"]["amount"]["min"]
            )
            self.price_steps[symbol] = elm["precision"]["price"]
            self.c_mults[symbol] = elm["contractSize"]
            
            # DYDX leverage varies by market
            self.max_leverage[symbol] = (
                int(elm["info"]["maxLeverage"]) if "maxLeverage" in elm["info"] else 10
            )

    async def watch_balance(self):
        """Watch balance updates using DYDX API"""
        res = None
        while True:
            try:
                if self.stop_websocket:
                    break
                
                # Fetch account info from DYDX
                account_info = await self.fetch_account_info()
                if account_info:
                    # Convert to expected format
                    balance_update = {
                        self.quote: {
                            "total": float(account_info.get("equity", 0)),
                            "free": float(account_info.get("freeCollateral", 0)),
                            "used": float(account_info.get("equity", 0)) - float(account_info.get("freeCollateral", 0))
                        }
                    }
                    self.handle_balance_update(balance_update)
                
                await asyncio.sleep(10)
            except Exception as e:
                logging.error(f"exception watch_balance {res} {e}")
                traceback.print_exc()
                await asyncio.sleep(1)

    async def watch_orders(self):
        """Watch order updates using DYDX API"""
        res = None
        while True:
            try:
                if self.stop_websocket:
                    break
                
                # Fetch open orders from DYDX
                orders = await self.fetch_open_orders()
                if orders:
                    # Convert to expected format and handle updates
                    for order in orders:
                        order["position_side"] = self.determine_pos_side(order)
                        order["qty"] = order["amount"]
                    self.handle_order_update(orders)
                
                await asyncio.sleep(5)
            except Exception as e:
                logging.error(f"exception watch_orders {res} {e}")
                traceback.print_exc()
                await asyncio.sleep(1)

    def determine_pos_side(self, order):
        """Determine position side for DYDX orders"""
        # DYDX is not hedge mode
        if order["symbol"] in self.positions:
            if self.positions[order["symbol"]]["long"]["size"] != 0.0:
                return "long"
            elif self.positions[order["symbol"]]["short"]["size"] != 0.0:
                return "short"
            else:
                return "long" if order["side"] == "buy" else "short"
        else:
            return "long" if order["side"] == "buy" else "short"

    async def fetch_account_info(self):
        """Fetch account information from DYDX"""
        try:
            # DYDX uses subaccount 0 by default
            response = await self.indexer_client.account.get_subaccount(
                address=self.wallet_address,
                subaccount_number=0
            )
            if response and "subaccount" in response:
                return response["subaccount"]
            return None
        except Exception as e:
            logging.error(f"Error fetching account info: {e}")
            return None

    async def fetch_open_orders(self, symbol: str = None):
        """Fetch open orders from DYDX"""
        try:
            response = await self.indexer_client.account.get_orders(
                address=self.wallet_address,
                market=symbol.replace("/USD:USD", "-USD") if symbol else None,
                status="OPEN"
            )
            
            if response and "orders" in response:
                orders = []
                for order_data in response["orders"]:
                    order = self.convert_dydx_order_to_standard(order_data)
                    orders.append(order)
                return sorted(orders, key=lambda x: x["timestamp"])
            return []
        except Exception as e:
            logging.error(f"Error fetching open orders: {e}")
            return []

    def convert_dydx_order_to_standard(self, dydx_order):
        """Convert DYDX order format to standard format"""
        return {
            "id": dydx_order["id"],
            "symbol": dydx_order["market"].replace("-USD", "/USD:USD"),
            "side": dydx_order["side"].lower(),
            "amount": float(dydx_order["size"]),
            "price": float(dydx_order["price"]),
            "type": dydx_order["type"].lower(),
            "timestamp": int(dydx_order["createdAtHeight"]) * 1000,  # Convert to ms
            "status": dydx_order["status"].lower(),
            "reduce_only": dydx_order.get("reduceOnly", False),
            "info": dydx_order
        }

    async def fetch_positions(self) -> ([dict], float):
        """Fetch positions and balance from DYDX"""
        try:
            # Fetch account info
            account_info = await self.fetch_account_info()
            if not account_info:
                return [], 0.0
            
            balance = float(account_info.get("equity", 0))
            
            # Fetch positions
            response = await self.indexer_client.account.get_positions(
                address=self.wallet_address,
                status="OPEN"
            )
            
            positions = []
            if response and "positions" in response:
                for pos_data in response["positions"]:
                    size = float(pos_data["size"])
                    if size != 0:
                        position = {
                            "symbol": pos_data["market"].replace("-USD", "/USD:USD"),
                            "position_side": "long" if size > 0 else "short",
                            "size": abs(size),
                            "price": float(pos_data["entryPrice"]),
                        }
                        positions.append(position)
            
            return positions, balance
        except Exception as e:
            logging.error(f"Error fetching positions and balance: {e}")
            return [], 0.0

    async def fetch_tickers(self):
        """Fetch ticker data from DYDX"""
        try:
            response = await self.indexer_client.markets.get_perpetual_markets()
            if response and "markets" in response:
                tickers = {}
                for market_id, market_data in response["markets"].items():
                    symbol = market_id.replace("-USD", "/USD:USD")
                    price = float(market_data.get("oraclePrice", 0))
                    tickers[symbol] = {
                        "bid": price,
                        "ask": price,
                        "last": price,
                    }
                return tickers
            return {}
        except Exception as e:
            logging.error(f"Error fetching tickers: {e}")
            return {}

    async def fetch_ohlcv(self, symbol: str, timeframe="1m"):
        """Fetch OHLCV data from DYDX"""
        try:
            # Convert symbol format
            market = symbol.replace("/USD:USD", "-USD")

            # Map timeframes
            tf_map = {"1m": "1MIN", "5m": "5MINS", "15m": "15MINS", "1h": "1HOUR", "4h": "4HOURS"}
            resolution = tf_map.get(timeframe, "1MIN")

            response = await self.indexer_client.markets.get_perpetual_market_candles(
                market=market,
                resolution=resolution,
                limit=100
            )

            if response and "candles" in response:
                ohlcv = []
                for candle in response["candles"]:
                    ohlcv.append([
                        int(candle["startedAt"]),  # timestamp
                        float(candle["open"]),     # open
                        float(candle["high"]),     # high
                        float(candle["low"]),      # low
                        float(candle["close"]),    # close
                        float(candle["baseTokenVolume"])  # volume
                    ])
                return sorted(ohlcv, key=lambda x: x[0])
            return []
        except Exception as e:
            logging.error(f"Error fetching OHLCV for {symbol}: {e}")
            return []

    async def fetch_ohlcvs_1m(self, symbol: str, since: float = None, limit=None):
        """Fetch 1-minute OHLCV data"""
        return await self.fetch_ohlcv(symbol, timeframe="1m")

    async def execute_order(self, order: dict) -> dict:
        """Execute an order on DYDX"""
        try:
            if self.node_client is None:
                logging.error("NodeClient not initialized - order execution not available yet")
                return {}

            # Convert symbol format
            market = order["symbol"].replace("/USD:USD", "-USD")

            # Create order using DYDX Order class
            dydx_order = Order(
                market=market,
                side=order["side"].upper(),
                size=str(order["qty"]),
                price=str(order["price"]),
                reduce_only=order.get("reduce_only", False),
                post_only=self.config["live"]["time_in_force"] == "post_only"
            )

            # Execute the order using node client
            executed = await self.node_client.post_order(dydx_order)

            if executed:
                # Convert response to standard format
                return self.convert_dydx_order_to_standard(executed)
            return {}

        except Exception as e:
            logging.error(f"Error executing order {order}: {e}")
            traceback.print_exc()
            return {}

    async def execute_orders(self, orders: [dict]) -> [dict]:
        """Execute multiple orders"""
        return await self.execute_multiple(orders, "execute_order")

    async def execute_cancellation(self, order: dict) -> dict:
        """Cancel an order on DYDX"""
        try:
            if self.node_client is None:
                logging.error("NodeClient not initialized - order cancellation not available yet")
                return {}

            executed = await self.node_client.cancel_order(order["id"])
            return executed if executed else {}
        except Exception as e:
            logging.error(f"Error cancelling order {order}: {e}")
            traceback.print_exc()
            return {}

    async def execute_cancellations(self, orders: [dict]) -> [dict]:
        """Cancel multiple orders"""
        return await self.execute_multiple(orders, "execute_cancellation")

    def did_create_order(self, executed) -> bool:
        """Check if order was successfully created"""
        try:
            return executed and "id" in executed and executed.get("status") in ["open", "pending"]
        except:
            return False

    def did_cancel_order(self, cancelled) -> bool:
        """Check if order was successfully cancelled"""
        try:
            return cancelled and "status" in cancelled and cancelled["status"] == "canceled"
        except:
            return False

    async def fetch_pnls(self, start_time: int = None, end_time: int = None, limit=None):
        """Fetch PnL data from DYDX"""
        try:
            if limit is None:
                limit = 100

            response = await self.indexer_client.account.get_fills(
                address=self.wallet_address,
                limit=limit
            )

            if response and "fills" in response:
                pnls = []
                for fill in response["fills"]:
                    pnl_entry = {
                        "id": fill["id"],
                        "timestamp": int(fill["createdAtHeight"]) * 1000,
                        "symbol": fill["market"].replace("-USD", "/USD:USD"),
                        "side": fill["side"].lower(),
                        "amount": float(fill["size"]),
                        "price": float(fill["price"]),
                        "pnl": float(fill.get("fee", 0)) * -1,  # Fees as negative PnL
                        "position_side": "long" if fill["side"].lower() == "buy" else "short",
                        "info": fill
                    }
                    pnls.append(pnl_entry)
                return sorted(pnls, key=lambda x: x["timestamp"])
            return []
        except Exception as e:
            logging.error(f"Error fetching PnLs: {e}")
            return []

    async def fetch_pnl(self, start_time: int = None, limit=None):
        """Fetch PnL data (alias for fetch_pnls)"""
        return await self.fetch_pnls(start_time=start_time, limit=limit)

    def symbol_is_eligible(self, symbol):
        """Check if symbol is eligible for trading"""
        try:
            if symbol not in self.markets_dict:
                return False

            market_info = self.markets_dict[symbol]

            # Check if market is active
            if not market_info.get("active", True):
                return False

            # Check if market has sufficient volume/open interest
            if "info" in market_info:
                open_interest = float(market_info["info"].get("openInterest", 0))
                if open_interest == 0.0:
                    return False

            return True
        except Exception as e:
            logging.error(f"Error checking symbol eligibility {symbol}: {e}")
            return False

    async def update_exchange_config_by_symbols(self, symbols):
        """Update exchange configuration for specific symbols"""
        # DYDX doesn't require specific configuration updates like leverage setting
        # as it's handled per-order
        for symbol in symbols:
            logging.info(f"{symbol}: DYDX configuration updated")

    async def update_exchange_config(self):
        """Update general exchange configuration"""
        pass
