{"/USD:USD": {"id": "TIBBIR-USD", "symbol": "/USD:USD", "base": "", "quote": "USD", "settle": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "contractSize": -2.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1000000.0, "price": 1e-07}, "limits": {"amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1.0, "max": null}}, "maker": 0.0002, "taker": 0.0005, "percentage": true, "tierBased": true, "info": {"clobPairId": "351", "ticker": "TIBBIR-USD", "status": "ACTIVE", "oraclePrice": "0.0020689181449", "priceChange24H": "0.0010674790364", "volume24H": "300", "trades24H": 1, "nextFundingRate": "0.0000125", "initialMarginFraction": "0.05", "maintenanceMarginFraction": "0.03", "openInterest": "100000", "atomicResolution": -2, "quantumConversionExponent": -9, "tickSize": "0.0000001", "stepSize": "10000", "stepBaseQuantums": 1000000, "subticksPerTick": 1000000, "marketType": "ISOLATED", "openInterestLowerCap": "500000", "openInterestUpperCap": "1000000", "baseOpenInterest": "0", "defaultFundingRate1H": "0.0000125"}}}