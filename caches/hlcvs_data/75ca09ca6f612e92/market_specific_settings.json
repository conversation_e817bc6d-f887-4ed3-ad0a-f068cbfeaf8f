{"ETH": {"id": "ETHUSDT", "lowercaseId": "<PERSON><PERSON><PERSON>", "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01, "cost": null, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": 10000.0}, "price": {"min": 39.86, "max": 306177.0}, "cost": {"min": 20.0, "max": null}, "market": {"min": 0.001, "max": 2000.0}}, "marginModes": {"cross": true, "isolated": true}, "created": 1569398400000, "info": {"symbol": "ETHUSDT", "pair": "ETHUSDT", "contractType": "PERPETUAL", "deliveryDate": "4133404800000", "onboardDate": "1569398400000", "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "ETH", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": "2", "quantityPrecision": "3", "baseAssetPrecision": "8", "quotePrecision": "8", "underlyingType": "COIN", "underlyingSubType": ["Layer-1"], "triggerProtect": "0.0500", "liquidationFee": "0.012500", "marketTakeBound": "0.05", "maxMoveOrderLimit": "10000", "filters": [{"minPrice": "39.86", "filterType": "PRICE_FILTER", "tickSize": "0.01", "maxPrice": "306177"}, {"minQty": "0.001", "maxQty": "10000", "stepSize": "0.001", "filterType": "LOT_SIZE"}, {"minQty": "0.001", "stepSize": "0.001", "filterType": "MARKET_LOT_SIZE", "maxQty": "2000"}, {"filterType": "MAX_NUM_ORDERS", "limit": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": "10"}, {"notional": "20", "filterType": "MIN_NOTIONAL"}, {"multiplierUp": "1.0500", "multiplierDown": "0.9500", "multiplierDecimal": "4", "filterType": "PERCENT_PRICE"}, {"filterType": "POSITION_RISK_CONTROL", "positionControlSide": "NONE"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}, "tierBased": false, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": 1.0, "min_cost": 20.0, "price_step": 0.01, "min_qty": 0.001, "qty_step": 0.001, "exchange": "binanceusdm"}}