{"BTC": {"id": "BTCUSDT", "lowercaseId": "btcusdt", "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1, "cost": null, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": 1000.0}, "price": {"min": 556.8, "max": 4529764.0}, "cost": {"min": 100.0, "max": null}, "market": {"min": 0.001, "max": 120.0}}, "marginModes": {"cross": true, "isolated": true}, "created": 1569398400000, "info": {"symbol": "BTCUSDT", "pair": "BTCUSDT", "contractType": "PERPETUAL", "deliveryDate": "4133404800000", "onboardDate": "1569398400000", "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "BTC", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": "2", "quantityPrecision": "3", "baseAssetPrecision": "8", "quotePrecision": "8", "underlyingType": "COIN", "underlyingSubType": ["PoW"], "triggerProtect": "0.0500", "liquidationFee": "0.012500", "marketTakeBound": "0.05", "maxMoveOrderLimit": "10000", "filters": [{"filterType": "PRICE_FILTER", "maxPrice": "4529764", "minPrice": "556.80", "tickSize": "0.10"}, {"filterType": "LOT_SIZE", "stepSize": "0.001", "maxQty": "1000", "minQty": "0.001"}, {"stepSize": "0.001", "minQty": "0.001", "filterType": "MARKET_LOT_SIZE", "maxQty": "120"}, {"filterType": "MAX_NUM_ORDERS", "limit": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": "10"}, {"notional": "100", "filterType": "MIN_NOTIONAL"}, {"multiplierDecimal": "4", "multiplierDown": "0.9500", "multiplierUp": "1.0500", "filterType": "PERCENT_PRICE"}, {"filterType": "POSITION_RISK_CONTROL", "positionControlSide": "NONE"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}, "tierBased": false, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": 1.0, "min_cost": 100.0, "price_step": 0.1, "min_qty": 0.001, "qty_step": 0.001, "exchange": "binanceusdm"}}