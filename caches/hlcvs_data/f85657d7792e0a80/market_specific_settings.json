{"ETH": {"id": "ETH-USD", "symbol": "ETH/USD:USD", "base": "ETH", "quote": "USD", "settle": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "contractSize": -9.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1000000.0, "price": 0.1}, "limits": {"amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1.0, "max": null}}, "maker": 0.0002, "taker": 0.0005, "percentage": true, "tierBased": true, "info": {"clobPairId": "1", "ticker": "ETH-USD", "status": "ACTIVE", "oraclePrice": "2611.473999", "priceChange24H": "102.673999", "volume24H": "82622424.8845", "trades24H": 12459, "nextFundingRate": "0.00002152857142857143", "initialMarginFraction": "0.02", "maintenanceMarginFraction": "0.012", "openInterest": "29527.122", "atomicResolution": -9, "quantumConversionExponent": -9, "tickSize": "0.1", "stepSize": "0.001", "stepBaseQuantums": 1000000, "subticksPerTick": 100000, "marketType": "CROSS", "openInterestLowerCap": "0", "openInterestUpperCap": "0", "baseOpenInterest": "13993.985", "defaultFundingRate1H": "0"}, "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": -9.0, "min_cost": 1.0, "price_step": 0.1, "min_qty": 1000000.0, "qty_step": 1000000.0, "exchange": "dydx"}}