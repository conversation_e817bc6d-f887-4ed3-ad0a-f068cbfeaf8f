{"BTC": {"id": "BTCUSDT", "lowercaseId": "btcusdt", "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1, "cost": null, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": 1000.0}, "price": {"min": 556.8, "max": 4529764.0}, "cost": {"min": 100.0, "max": null}, "market": {"min": 0.001, "max": 120.0}}, "marginModes": {"cross": true, "isolated": true}, "created": 1569398400000, "info": {"symbol": "BTCUSDT", "pair": "BTCUSDT", "contractType": "PERPETUAL", "deliveryDate": "4133404800000", "onboardDate": "1569398400000", "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "BTC", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": "2", "quantityPrecision": "3", "baseAssetPrecision": "8", "quotePrecision": "8", "underlyingType": "COIN", "underlyingSubType": ["PoW"], "triggerProtect": "0.0500", "liquidationFee": "0.012500", "marketTakeBound": "0.05", "maxMoveOrderLimit": "10000", "filters": [{"filterType": "PRICE_FILTER", "maxPrice": "4529764", "minPrice": "556.80", "tickSize": "0.10"}, {"filterType": "LOT_SIZE", "stepSize": "0.001", "maxQty": "1000", "minQty": "0.001"}, {"stepSize": "0.001", "minQty": "0.001", "filterType": "MARKET_LOT_SIZE", "maxQty": "120"}, {"filterType": "MAX_NUM_ORDERS", "limit": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": "10"}, {"notional": "100", "filterType": "MIN_NOTIONAL"}, {"multiplierDecimal": "4", "multiplierDown": "0.9500", "multiplierUp": "1.0500", "filterType": "PERCENT_PRICE"}, {"filterType": "POSITION_RISK_CONTROL", "positionControlSide": "NONE"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}, "tierBased": false, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": 1.0, "min_cost": 100.0, "price_step": 0.1, "min_qty": 0.001, "qty_step": 0.001, "exchange": "binanceusdm"}, "DOGE": {"id": "DOGEUSDT", "lowercaseId": "<PERSON><PERSON><PERSON>", "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1.0, "price": 1e-05, "cost": null, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1.0, "max": 50000000.0}, "price": {"min": 0.00244, "max": 30.0}, "cost": {"min": 5.0, "max": null}, "market": {"min": 1.0, "max": 30000000.0}}, "marginModes": {"cross": true, "isolated": true}, "created": 1569398400000, "info": {"symbol": "DOGEUSDT", "pair": "DOGEUSDT", "contractType": "PERPETUAL", "deliveryDate": "4133404800000", "onboardDate": "1569398400000", "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "DOGE", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": "6", "quantityPrecision": "0", "baseAssetPrecision": "8", "quotePrecision": "8", "underlyingType": "COIN", "underlyingSubType": ["Meme"], "triggerProtect": "0.1000", "liquidationFee": "0.015000", "marketTakeBound": "0.10", "maxMoveOrderLimit": "10000", "filters": [{"minPrice": "0.002440", "tickSize": "0.000010", "filterType": "PRICE_FILTER", "maxPrice": "30"}, {"maxQty": "50000000", "stepSize": "1", "filterType": "LOT_SIZE", "minQty": "1"}, {"filterType": "MARKET_LOT_SIZE", "stepSize": "1", "maxQty": "30000000", "minQty": "1"}, {"filterType": "MAX_NUM_ORDERS", "limit": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": "10"}, {"notional": "5", "filterType": "MIN_NOTIONAL"}, {"multiplierUp": "1.1000", "multiplierDown": "0.9000", "filterType": "PERCENT_PRICE", "multiplierDecimal": "4"}, {"filterType": "POSITION_RISK_CONTROL", "positionControlSide": "NONE"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}, "tierBased": false, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": 1.0, "min_cost": 5.0, "price_step": 1e-05, "min_qty": 1.0, "qty_step": 1.0, "exchange": "binanceusdm"}, "ETH": {"id": "ETHUSDT", "lowercaseId": "<PERSON><PERSON><PERSON>", "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01, "cost": null, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": 10000.0}, "price": {"min": 39.86, "max": 306177.0}, "cost": {"min": 20.0, "max": null}, "market": {"min": 0.001, "max": 2000.0}}, "marginModes": {"cross": true, "isolated": true}, "created": 1569398400000, "info": {"symbol": "ETHUSDT", "pair": "ETHUSDT", "contractType": "PERPETUAL", "deliveryDate": "4133404800000", "onboardDate": "1569398400000", "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "ETH", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": "2", "quantityPrecision": "3", "baseAssetPrecision": "8", "quotePrecision": "8", "underlyingType": "COIN", "underlyingSubType": ["Layer-1"], "triggerProtect": "0.0500", "liquidationFee": "0.012500", "marketTakeBound": "0.05", "maxMoveOrderLimit": "10000", "filters": [{"minPrice": "39.86", "filterType": "PRICE_FILTER", "tickSize": "0.01", "maxPrice": "306177"}, {"minQty": "0.001", "maxQty": "10000", "stepSize": "0.001", "filterType": "LOT_SIZE"}, {"minQty": "0.001", "stepSize": "0.001", "filterType": "MARKET_LOT_SIZE", "maxQty": "2000"}, {"filterType": "MAX_NUM_ORDERS", "limit": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": "10"}, {"notional": "20", "filterType": "MIN_NOTIONAL"}, {"multiplierUp": "1.0500", "multiplierDown": "0.9500", "multiplierDecimal": "4", "filterType": "PERCENT_PRICE"}, {"filterType": "POSITION_RISK_CONTROL", "positionControlSide": "NONE"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}, "tierBased": false, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": 1.0, "min_cost": 20.0, "price_step": 0.01, "min_qty": 0.001, "qty_step": 0.001, "exchange": "binanceusdm"}, "SOL": {"id": "SOLUSDT", "lowercaseId": "solus<PERSON>", "symbol": "SOL/USDT:USDT", "base": "SOL", "quote": "USDT", "settle": "USDT", "baseId": "SOL", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01, "cost": null, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": 1000000.0}, "price": {"min": 0.42, "max": 6857.0}, "cost": {"min": 5.0, "max": null}, "market": {"min": 0.01, "max": 5000.0}}, "marginModes": {"cross": true, "isolated": true}, "created": 1569398400000, "info": {"symbol": "SOLUSDT", "pair": "SOLUSDT", "contractType": "PERPETUAL", "deliveryDate": "4133404800000", "onboardDate": "1569398400000", "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "SOL", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": "4", "quantityPrecision": "2", "baseAssetPrecision": "8", "quotePrecision": "8", "underlyingType": "COIN", "underlyingSubType": ["Layer-1"], "triggerProtect": "0.0500", "liquidationFee": "0.015000", "marketTakeBound": "0.05", "maxMoveOrderLimit": "10000", "filters": [{"filterType": "PRICE_FILTER", "minPrice": "0.4200", "maxPrice": "6857", "tickSize": "0.0100"}, {"stepSize": "0.01", "maxQty": "1000000", "filterType": "LOT_SIZE", "minQty": "0.01"}, {"maxQty": "5000", "filterType": "MARKET_LOT_SIZE", "minQty": "0.01", "stepSize": "0.01"}, {"filterType": "MAX_NUM_ORDERS", "limit": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": "10"}, {"filterType": "MIN_NOTIONAL", "notional": "5"}, {"multiplierDown": "0.9500", "multiplierDecimal": "4", "filterType": "PERCENT_PRICE", "multiplierUp": "1.0500"}, {"positionControlSide": "NONE", "filterType": "POSITION_RISK_CONTROL"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}, "tierBased": false, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": 1.0, "min_cost": 5.0, "price_step": 0.01, "min_qty": 0.01, "qty_step": 0.01, "exchange": "binanceusdm"}, "XRP": {"id": "XRPUSDT", "lowercaseId": "xrpusdt", "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001, "cost": null, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 10000000.0}, "price": {"min": 0.0143, "max": 100000.0}, "cost": {"min": 5.0, "max": null}, "market": {"min": 0.1, "max": 2000000.0}}, "marginModes": {"cross": true, "isolated": true}, "created": 1569398400000, "info": {"symbol": "XRPUSDT", "pair": "XRPUSDT", "contractType": "PERPETUAL", "deliveryDate": "4133404800000", "onboardDate": "1569398400000", "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "XRP", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": "4", "quantityPrecision": "1", "baseAssetPrecision": "8", "quotePrecision": "8", "underlyingType": "COIN", "underlyingSubType": ["Payment"], "triggerProtect": "0.0500", "liquidationFee": "0.012500", "marketTakeBound": "0.05", "maxMoveOrderLimit": "10000", "filters": [{"filterType": "PRICE_FILTER", "tickSize": "0.0001", "maxPrice": "100000", "minPrice": "0.0143"}, {"minQty": "0.1", "stepSize": "0.1", "maxQty": "10000000", "filterType": "LOT_SIZE"}, {"filterType": "MARKET_LOT_SIZE", "stepSize": "0.1", "maxQty": "2000000", "minQty": "0.1"}, {"limit": "200", "filterType": "MAX_NUM_ORDERS"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": "10"}, {"notional": "5", "filterType": "MIN_NOTIONAL"}, {"filterType": "PERCENT_PRICE", "multiplierUp": "1.0500", "multiplierDown": "0.9500", "multiplierDecimal": "4"}, {"positionControlSide": "NONE", "filterType": "POSITION_RISK_CONTROL"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}, "tierBased": false, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.0005, "c_mult": 1.0, "min_cost": 5.0, "price_step": 0.0001, "min_qty": 0.1, "qty_step": 0.1, "exchange": "binanceusdm"}}