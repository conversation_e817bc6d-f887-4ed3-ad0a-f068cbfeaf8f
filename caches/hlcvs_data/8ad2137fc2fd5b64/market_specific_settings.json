{"BTC": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00055, "maker": 0.0002, "contractSize": 1.0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1, "cost": null, "base": null, "quote": null}, "limits": {"leverage": {"min": 1.0, "max": 100.0}, "amount": {"min": 0.001, "max": 1190.0}, "price": {"min": 0.1, "max": 1999999.8}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1584230400000, "info": {"symbol": "BTCUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "BTC", "quoteCoin": "USDT", "launchTime": "1584230400000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "2", "leverageFilter": {"minLeverage": "1", "maxLeverage": "100.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.10", "maxPrice": "1999999.80", "tickSize": "0.10"}, "lotSizeFilter": {"maxOrderQty": "1190.000", "minOrderQty": "0.001", "qtyStep": "0.001", "postOnlyMaxOrderQty": "1190.000", "maxMktOrderQty": "119.000", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.005", "lowerFundingRate": "-0.005", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.01", "priceLimitRatioY": "0.02"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get", "hedge_mode": true, "maker_fee": 0.0002, "taker_fee": 0.00055, "c_mult": 1.0, "min_cost": 0.01, "price_step": 0.1, "min_qty": 0.001, "qty_step": 0.001, "exchange": "bybit"}}