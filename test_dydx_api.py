#!/usr/bin/env python3

"""
Test script to understand DYDX v4 client API structure
"""

try:
    from dydx_v4_client import *
    print("Available imports:")
    print(dir())
    
    print("\nTrying to import specific classes...")
    
    try:
        from dydx_v4_client.node import NodeClient
        print("✓ NodeClient imported")
    except ImportError as e:
        print(f"✗ NodeClient import failed: {e}")
    
    try:
        from dydx_v4_client.indexer import IndexerClient
        print("✓ IndexerClient imported")
    except ImportError as e:
        print(f"✗ IndexerClient import failed: {e}")
    
    try:
        from dydx_v4_client.wallet import Wallet
        print("✓ Wallet imported")
    except ImportError as e:
        print(f"✗ Wallet import failed: {e}")
    
    try:
        from dydx_v4_client.network import Network
        print("✓ Network imported")
    except ImportError as e:
        print(f"✗ Network import failed: {e}")
        
    # Try alternative imports
    try:
        from dydx_v4_client import NodeClient, IndexerClient, Network
        print("✓ Alternative import successful")
    except ImportError as e:
        print(f"✗ Alternative import failed: {e}")
        
    # Check what's actually available
    import dydx_v4_client
    print(f"\nActual dydx_v4_client contents: {dir(dydx_v4_client)}")
    
except ImportError as e:
    print(f"Failed to import dydx_v4_client: {e}")
